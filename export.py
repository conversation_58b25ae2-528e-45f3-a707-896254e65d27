import pandas as pd
import os
import pymysql
from sqlalchemy import create_engine
import datetime


def get_engine():
    """
    创建SQLAlchemy引擎用于pandas读取
    """
    try:
        # 创建数据库连接字符串
        connection_str = "mysql+pymysql://root:@localhost/soft?charset=utf8mb4"
        engine = create_engine(connection_str)
        return engine
    except Exception as e:
        print(f"创建SQLAlchemy引擎时出错: {e}")
        return None


def export_softpv_to_excel(output_path=None):
    try:
        # 使用SQLAlchemy引擎
        engine = get_engine()
        if not engine:
            return None

        # 使用pandas从MySQL读取数据
        query = "SELECT * FROM softpv"
        df = pd.read_sql(query, engine)

        query1 = "SELECT * FROM soft"
        df1 = pd.read_sql(query1, engine)

        df = pd.merge(df, df1, left_on="softid", right_on="id")

        # 设置默认输出路径
        if output_path is None:
            output_path = "softpv_export.xlsx"

        # 导出到Excel
        df.to_excel(output_path, index=False)  # 使用utf-8-sig以支持Excel中文显示

        print(f"数据已成功导出到: {output_path}")
        print(f"共导出 {len(df)} 条记录")
        return output_path

    except Exception as e:
        print(f"导出数据时出错: {e}")
        return None


def main():
    # 导出基本数据
    export_softpv_to_excel()


if __name__ == "__main__":
    main()
