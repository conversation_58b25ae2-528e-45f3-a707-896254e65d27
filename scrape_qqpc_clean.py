import os
import time

from bs4 import BeautifulSoup
import requests

def get_software_names_with_pagination(base_url, max_pages=5, delay_seconds=1.0):
    """
    从给定URL抓取软件名称，支持分页
    
    参数:
        base_url: 基础URL，如 https://pc.qq.com/category/c5.html
        max_pages: 最大分页数，默认为5
        delay_seconds: 请求间隔时间（秒），默认为1秒
    
    返回:
        软件名称列表
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    all_names = []
    current_url = base_url
    page_num = 1
    category_id = None
    
    # 从 URL 中提取分类 ID，例如从 https://pc.qq.com/category/c99.html 提取 c99
    if '.html' in base_url:
        parts = base_url.split('/')
        for part in parts:
            if part.startswith('c') and '.html' in part:
                category_id = part.split('.')[0]  # 提取分类 ID，如 c99
    
    print(f"    分类 ID: {category_id}")
    
    # 构造分页URL列表
    urls_to_visit = [base_url]  # 第一页
    
    # 添加后续分页URL
    if category_id:
        for i in range(1, max_pages):
            urls_to_visit.append(f"https://pc.qq.com/category/{category_id}-{i}.html")
    
    print(f"    计划抓取页面: {len(urls_to_visit)} 页")
    for i, url in enumerate(urls_to_visit):
        print(f"      页面 {i+1}: {url}")
    
    # 抓取每个页面
    for page_index, page_url in enumerate(urls_to_visit, 1):
        if page_index > max_pages:
            break
            
        try:
            print(f"  - 抓取子页面 {page_index}: {page_url}")
            
            response = requests.get(page_url, headers=headers)
            response.raise_for_status()  # 对HTTP错误引发异常
            
            # 确保响应内容编码正确
            response.encoding = 'utf-8'
            
            # 检查是否为404页面
            if "<title>404</title>" in response.text:
                print(f"    页面不存在（404）")
                continue  # 如果是404页面，跳过并尝试下一个URL
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找所有软件项目
            software_items = soup.select('.category-item.bottom-shadow.J_cat_item')
            
            # 如果没有找到标准的软件项目，尝试其他可能的选择器
            if not software_items:
                # 尝试其他可能的选择器
                software_items = soup.select('.soft-info-mid h3.soft-name a')  # 另一种可能的选择器
            
            # 如果还是没有找到，尝试更通用的方法
            if not software_items:
                # 查找所有可能是软件名称的链接
                all_links = soup.find_all('a')
                software_items = [link for link in all_links if link.get('href') and '/detail/' in link.get('href')]
            
            # 提取软件名称
            page_names = []
            for item in software_items:
                # 如果项目本身就是链接
                if item.name == 'a':
                    link = item
                else:
                    # 在项目中查找链接
                    link = item.find('a')
                
                if link:
                    software_name = link.text.strip()
                    if software_name and software_name not in all_names:  # 确保名称不为空且不重复
                        page_names.append(software_name)
                        all_names.append(software_name)
            
            if page_names:
                print(f"    找到 {len(page_names)} 个软件项目")
            else:
                print(f"    未找到软件项目")
            
            # 添加小延迟以避免过载服务器
            if page_index < len(urls_to_visit) - 1:
                print(f"    等待 {delay_seconds} 秒后继续...")
                time.sleep(delay_seconds)
                
        except requests.exceptions.RequestException as e:
            print(f"    获取URL {page_url} 时出错: {e}")
            continue  # 如果这个URL出错，尝试下一个URL
    
    return all_names

def main():
    # 在代码中直接设置参数
    # 要抓取的URL
    url = "https://pc.qq.com/category/c6.html"  # 推荐软件页面
    
    # 每个分类最多抓取的分页数
    max_pages = 288
    
    # 请求间隔时间（秒）
    delay_time = 1.0
    
    # 输出文件名
    output_filename = "游戏.txt"
    
    # 文件编码
    file_encoding = "utf-8-sig"  # 使用 UTF-8-BOM 编码，确保 Windows 记事本能正确显示中文
    
    print("开始抓取软件名称...")
    print(f"抓取URL: {url}")
    print(f"最大分页数: {max_pages}")
    print(f"请求间隔: {delay_time} 秒")
    
    # 抓取软件名称
    software_names = get_software_names_with_pagination(url, max_pages, delay_time)
    
    print(f"\n抓取完成。共找到 {len(software_names)} 个软件项目。")
    
    # 保存结果到文件
    output_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), output_filename)
    try:
        # 使用指定的编码保存
        with open(output_file, "w", encoding=file_encoding) as f:
            for name in software_names:
                f.write(f"{name}\n")
        print(f"结果已保存到 {output_file}")
        print(f"文件编码: {file_encoding}")
        print("请使用支持UTF-8编码的文本编辑器（如记事本、VS Code等）打开文件查看。")
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    main()
