import pymysql
import json
import requests
import datetime
import concurrent.futures
import time
import logging
import sys
import threading
import random
from insermysql import connect_to_mysql

# 配置日志
logging.basicConfig(
    level=logging.INFO,  # 将日志级别提高到INFO，不输出DEBUG日志
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("baidu_api.log"),
        logging.StreamHandler(sys.stdout)
    ]
)


def query_keyword_traffic(word):
    """查询百度API获取关键词流量数据"""
    logging.info(f"开始查询关键词: {word}")
    url = "https://api.baidu.com/json/sms/service/PvSearchFunction/getPvSearch"
    user_payload = {
        "header": {
            "userName": "SF海鸟科技01",
            "accessToken": "eyJhbGciOiJIUzM4NCJ9.eyJzdWIiOiJhY2MiLCJhdWQiOiLml6Dlv6for4rmiYAiLCJ1aWQiOjQ0MDY1MTk3LCJhcHBJZCI6IjBhNmI3YWVkNDcwYzg4Y2YxNGE5MTA0NjVlNDhmNDFkIiwiaXNzIjoi5ZWG5Lia5byA5Y-R6ICF5Lit5b-DIiwicGxhdGZvcm1JZCI6IjQ5NjAzNDU5NjU5NTg1NjE3OTQiLCJleHAiOjQxMDI0MTYwMDAsImp0aSI6Ijc3NzQyOTY2NjI5OTc0ODM1MzQifQ.4VJeTM3WRBu8zr9fGX2qSPCPd78OPv5CV8zxYt53Tkxig-CcnBSu_GkzkmhyFpp2",
            "action": "API-PYTHON"
        },
        "body": {
            "bidWordSource": "wordList",
            "device": 2,
            "orderBy": "show",
            "order": "desc",
            "keywordList": [
                {
                    "keywordName": word,
                    "matchType": 1,
                    "phraseType": 1
                }
            ]
        }
    }
    http_headers = {
        "Accept-Encoding": "gzip, deflate",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    user_payload = json.dumps(user_payload)
    logging.debug(f"发送API请求: {url}")
    try:
        resp = requests.request("POST", url, data=user_payload, headers=http_headers)
        logging.debug(f"API响应状态码: {resp.status_code}")
        json_response = resp.json()
        logging.debug(f"API响应头部: {json_response.get('header', {})}")
        return json_response
    except Exception as e:
        logging.error(f"API请求失败: {e}")
        raise


def fetch_soft_data(connection):
    """从soft表中获取softname和id"""
    logging.info("开始从soft表获取数据")
    try:
        with connection.cursor() as cursor:
            # 查询soft表获取softname和id
            sql = "SELECT id, softname FROM soft"
            cursor.execute(sql)
            results = cursor.fetchall()
            logging.info(f"从soft表中获取了 {len(results)} 条记录")
            return results
    except Exception as e:
        logging.error(f"从soft表获取数据时出错: {e}")
        return []



def get_keyword_data(word, api_data):
    """从百度API响应中提取关键词数据"""
    try:
        # 检查API响应结构
        if 'body' not in api_data or 'data' not in api_data['body']:
            logging.error(f"无效的API响应结构: {word}")
            return None
            
        word_list = api_data['body']['data']
        if not word_list or len(word_list) == 0:
            logging.error(f"无搜索结果: {word}")
            return None
            
        # 获取第一个数据项的data字段
        if not isinstance(word_list, list):
            logging.error(f"API响应结构不是预期的列表格式: {word}")
            return None
            
        first_item = word_list[0]
        if 'data' not in first_item or not isinstance(first_item['data'], list):
            logging.error(f"第一个数据项缺少data字段或格式不正确: {word}")
            return None
            
        items = first_item['data']
        
        # 循环查找匹配的关键词
        for item in items:
            if 'keywordName' in item and item['keywordName'].lower() == word.lower():
                # 返回包含多个字段的字典
                result = {
                    'keywordName': item.get('keywordName', word),
                    'pcPv': item.get('pcPv', -1),
                    'pcShow': item.get('pcShow', -1),
                    'pcClick': item.get('pcClick', -1),
                    'price': item.get('price', -1),
                    'kwc': item.get('kwc', -1),
                    'averageMonthPvPc': item.get('averageMonthPvPc', -1)
                }
                # 只记录错误日志，不记录成功日志
                return result
        
        logging.error(f"未找到匹配关键词: {word}")
        return None
    except Exception as e:
        logging.error(f"查询关键词 {word} 时出错: {str(e)}")
        return None


def update_softpv_data(connection, softid, api_data, keyword):
    """使用百度API数据插入到softpv表，不进行更新操作"""
    try:        
        # 使用新的方法提取关键词数据
        keyword_data = get_keyword_data(keyword, api_data)
        
        # 如果没有数据，使用默认值
        if not keyword_data:
            # 使用默认值
            keyword_data = {
                'keywordName': keyword,
                'pcPv': 0,
                'pcShow': 0,
                'pcClick': 0,
                'price': 0.0,
                'kwc': 0,
                'averageMonthPvPc': 0
            }
            
        # 提取字段
        name = keyword_data.get('keywordName', keyword)
        pcPv = keyword_data.get('pcPv', 0)
        pcShow = keyword_data.get('pcShow', 0)
        pcClick = keyword_data.get('pcClick', 0)
        price = keyword_data.get('price', 0.0)
        kwc = keyword_data.get('kwc', 0)
        averageMonthPvPc = keyword_data.get('averageMonthPvPc', 0)
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 直接写入数据，不创建表（假设表已存在）
        try:
            # 使用简化的SQL语句
            insert_sql = """
            INSERT INTO softpv 
            (name, softid, pcPv, pcShow, pcClick, price, kwc, averageMonthPvPc, updated_at) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            # 使用新的数据库连接执行操作，避免连接过期
            new_connection = connect_to_mysql()
            if not new_connection:
                logging.error("无法连接到数据库")
                return False

            try:
                with new_connection.cursor() as cursor:
                    cursor.execute(insert_sql, (
                        name, softid, pcPv, pcShow, pcClick, price, kwc,
                        averageMonthPvPc, current_time
                    ))
                    # 提交事务
                    new_connection.commit()
                    return True
            except Exception as db_error:
                logging.error(f"插入数据库时出错: {db_error}")
                return False
            finally:
                # 确保连接关闭
                new_connection.close()

        except Exception as db_error:
            logging.error(f"数据库操作错误: {db_error}")
            return False

    except Exception as e:
        logging.error(f"处理softid为 {softid} 的数据时出错: {e}")
        return False


def process_keyword(item, connection):
    """
    处理单个关键词并更新数据库
    """
    thread_id = threading.get_ident()
    try:
        # 确保 softid 是整数
        try:
            softid = int(item['id'])
            if softid <= 0:
                logging.error(f"softid无效: {softid}")
                return f"关键词处理失败: softid无效 {softid}"
        except (ValueError, TypeError) as e:
            logging.error(f"softid类型错误: {item['id']}, 错误: {e}")
            return f"关键词处理失败: softid类型错误 {item['id']}"
            
        softname = item['softname']
        if not softname or len(softname.strip()) == 0:
            logging.error(f"softname为空, softid: {softid}")
            return f"关键词处理失败: softname为空, softid: {softid}"
        
        logging.info(f"[线程 {thread_id % 10000}] 正在处理关键词: {softname} (ID: {softid})")
        
        # 添加重试机制
        max_retries = 3
        retry_count = 0
        success = False
        last_error = None
        
        while retry_count < max_retries and not success:
            try:
                # 查询百度API获取关键词流量
                api_response = query_keyword_traffic(softname)
                
                # 检查API响应是否存在
                if not api_response:
                    logging.warning(f"[线程 {thread_id % 10000}] API返回空响应，关键词: {softname}，重试 {retry_count + 1}/{max_retries}")
                    retry_count += 1
                    time.sleep(2)  # 重试前等待更长时间
                    continue
                
                # 检查API响应是否成功
                if 'header' in api_response and api_response['header'].get('status', -1) == 0:
                    # 使用结果插入到softpv表
                    result = update_softpv_data(connection, softid, api_response, softname)
                    # 即使数据库操作失败，也标记为成功，避免重试API调用
                    success = True
                else:
                    error_msg = api_response.get('header', {}).get('desc', '未知错误')
                    retry_count += 1
                    time.sleep(2)  # 重试前等待更长时间
                    continue
            except Exception as e:
                logging.error(f"[线程 {thread_id % 10000}] API请求失败: {e}，重试 {retry_count + 1}/{max_retries}")
                last_error = e
                retry_count += 1
                time.sleep(2)  # 重试前等待更长时间
        
        # 添加一个更长的延迟，避免API请求过于频繁
        time.sleep(2)
        
        if success:
            return f"关键词 {softname} (ID: {softid}) 处理成功"
        else:
            # 如果所有重试都失败，尝试使用默认值插入
            logging.warning(f"[线程 {thread_id % 10000}] 所有重试失败，使用默认值处理softid: {softid}, 关键词: {softname}")
            # 创建一个空的API响应
            default_response = {
                "header": {"status": 0, "desc": "default_response"},
                "body": {}
            }
            # 即使插入失败也不抛出异常
            update_softpv_data(connection, softid, default_response, softname)
            return f"关键词 {softname} (ID: {softid}) 处理完成"
    except Exception as e:
        return f"处理关键词 {softname} (ID: {softid}) 时出错: {e}"


def process_keyword_batch(keyword_batch, connection, batch_index):
    """处理一组关键词批量"""
    # 去掉处理开始的日志
    
    # 设置线程池最大工作线程数
    max_workers = 2  # 每组内并发数
    
    # 使用线程池并行处理关键词
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 创建任务列表
        futures = []
        for item in keyword_batch:
            # 提交任务到线程池
            future = executor.submit(process_keyword, item, connection)
            futures.append(future)
        
        # 处理任务结果
        results = []
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                logging.info(result)
                results.append(result)
            except Exception as e:
                logging.error(f"处理关键词结果时出错: {e}")
                results.append(f"处理失败: {e}")
    
    # 去掉处理完成的日志
    return results




def main():

    # 连接到MySQL
    connection = connect_to_mysql()
    if not connection:
        logging.error("连接MySQL数据库失败")
        return
    
    try:
        # 从 soft表获取数据
        soft_data = fetch_soft_data(connection)
        
        # 检查数据有效性
        valid_data = []
        for item in soft_data:
            try:
                if 'id' not in item or 'softname' not in item:
                    continue
                    
                softid = int(item['id'])
                softname = item['softname']
                
                if softid <= 0 or not softname or len(softname.strip()) == 0:
                    continue
                    
                valid_data.append(item)
            except (ValueError, TypeError):
                pass
                
        logging.info(f"有效数据项: {len(valid_data)}/{len(soft_data)}")
        
        # 将关键词分组，每组5个
        batch_size = 5
        keyword_batches = [valid_data[i:i + batch_size] for i in range(0, len(valid_data), batch_size)]
        
        # 处理每一组关键词
        for i, batch in enumerate(keyword_batches):
            try:
                # 处理当前组
                process_keyword_batch(batch, connection, i + 1)
                
                # 如果不是最后一组，暂停1-2秒
                if i < len(keyword_batches) - 1:
                    time.sleep(random.uniform(1.0, 2.0))
            except Exception as e:
                logging.error(f"处理第 {i + 1} 组关键词时出错: {e}")
                # 继续处理下一组
        
    except Exception as e:
        logging.error(f"发生错误: {e}")
    finally:
        # 关闭连接
        connection.close()


if __name__ == "__main__":
    main()
