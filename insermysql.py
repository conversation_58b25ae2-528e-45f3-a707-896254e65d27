import os
import time
import pymysql


def connect_to_mysql():
    """
    连接到MySQL数据库并返回连接对象
    """
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='',  # 替换为你的实际密码
            database='soft',  # 替换为你的实际数据库名称
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        return connection
    except Exception as e:
        print(f"连接MySQL时出错: {e}")
        return None


def check_table_structure(connection):
    """
    检查soft表的结构，获取字段名称
    """
    try:
        with connection.cursor() as cursor:
            # 查询表结构
            cursor.execute("DESCRIBE soft")
            columns = cursor.fetchall()
            
            # 打印表结构信息
            print("表结构信息:")
            column_names = []
            for column in columns:
                print(f"字段名: {column['Field']}, 类型: {column['Type']}")
                column_names.append(column['Field'])
            
            return column_names
    except Exception as e:
        print(f"检查表结构时出错: {e}")
        return None


def process_text_files(connection):
    """
    处理data目录中的所有文本文件并将其内容插入到数据库中
    """
    # 首先检查表结构
    column_names = check_table_structure(connection)
    if not column_names:
        print("无法获取表结构，操作终止")
        return
    
    # 确定正确的字段名
    category_field = None
    content_field = None
    
    # 尝试找到类别和内容字段
    possible_category_fields = ['catname', 'catename', 'category', 'cat', 'type', 'typename']
    possible_content_fields = ['softname', 'content', 'text', 'description', 'soft_content']
    
    for field in possible_category_fields:
        if field in column_names:
            category_field = field
            break
    
    for field in possible_content_fields:
        if field in column_names:
            content_field = field
            break
    
    if not category_field or not content_field:
        print(f"找不到合适的类别字段和内容字段。可用字段: {column_names}")
        return
    
    print(f"将使用 {category_field} 作为类别字段，{content_field} 作为内容字段")
    
    # 获取data目录的绝对路径
    data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    
    # 检查目录是否存在
    if not os.path.exists(data_dir):
        print(f"错误: 目录 {data_dir} 不存在")
        return
    
    try:
        with connection.cursor() as cursor:
            # 遍历data目录中的所有文件
            for filename in os.listdir(data_dir):
                if filename.endswith('.txt'):
                    file_path = os.path.join(data_dir, filename)
                    
                    # 提取catname（不带扩展名的文件名）
                    catname = os.path.splitext(filename)[0]
                    
                    # 读取文件内容，每行作为一条记录
                    try:
                        with open(file_path, 'r', encoding='utf-8') as file:
                            # 计数器，用于记录插入的行数
                            line_count = 0
                            
                            # 逐行读取文件
                            for line in file:
                                # 去除行首行尾的空白字符
                                line = line.strip()
                                
                                # 跳过空行
                                if not line:
                                    continue
                                    
                                # 构建动态SQL语句
                                sql = f"INSERT INTO soft ({category_field}, {content_field}) VALUES (%s, %s)"
                                cursor.execute(sql, (catname, line))
                                line_count += 1
                            
                            print(f"已将 {filename} 的 {line_count} 行数据插入数据库")
                    except Exception as e:
                        print(f"处理文件 {filename} 时出错: {e}")
        
        # 提交事务
        connection.commit()
        print("所有文件处理成功")
    except Exception as e:
        print(f"数据库操作过程中出错: {e}")


def main():
    # 连接到MySQL
    connection = connect_to_mysql()
    if not connection:
        return
    
    try:
        # 处理文本文件并插入数据库
        process_text_files(connection)
        
    finally:
        # 关闭连接
        connection.close()
        print("MySQL连接已关闭")


if __name__ == "__main__":
    main()
